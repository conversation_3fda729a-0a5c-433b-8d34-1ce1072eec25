/**
 * Not Found page
 * Displays when a page cannot be found
 */
import Link from 'next/link';
import { Button } from '@/components/ui/button';
import { FileQuestion } from 'lucide-react';

export default function NotFound() {
  return (
    <div className="flex min-h-screen flex-col items-center justify-center bg-gradient-to-b from-black to-gray-900 px-4 text-center text-white">
      <div className="mx-auto flex h-20 w-20 items-center justify-center rounded-full bg-blue-100 mb-6">
        <FileQuestion className="h-10 w-10 text-blue-600" />
      </div>
      
      <h1 className="text-4xl font-bold mb-2">404</h1>
      <h2 className="text-2xl font-semibold mb-4">找不到頁面</h2>
      
      <p className="text-gray-300 mb-8 max-w-md">
        您嘗試訪問的頁面可能已被移除、名稱變更，或是暫時無法使用。
      </p>
      
      <Link href="/">
        <Button variant="glow">
          返回首頁
        </Button>
      </Link>
    </div>
  );
}
