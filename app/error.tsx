/**
 * Error boundary component
 * Displays friendly error message when unexpected errors occur
 */
'use client';

import { useEffect } from 'react';
import Link from 'next/link';
import { Button } from '@/components/ui/button';
import { AlertTriangle } from 'lucide-react';

interface ErrorProps {
  error: Error & { digest?: string };
  reset: () => void;
}

export default function Error({ error, reset }: ErrorProps) {
  useEffect(() => {
    // Log error to an error reporting service
    console.error('Application error:', error);
  }, [error]);

  return (
    <div className="flex min-h-screen flex-col items-center justify-center bg-gradient-to-b from-black to-gray-900 px-4 text-center text-white">
      <div className="mx-auto flex h-20 w-20 items-center justify-center rounded-full bg-red-100 mb-6">
        <AlertTriangle className="h-10 w-10 text-red-600" />
      </div>
      
      <h1 className="text-3xl font-bold mb-2">發生了一些問題</h1>
      
      <p className="text-gray-300 mb-8 max-w-md">
        很抱歉，我們無法處理您的請求。請稍後再試或聯絡我們的支援團隊。
      </p>
      
      <div className="flex flex-col sm:flex-row gap-4">
        <Button onClick={reset} variant="glow">
          重試
        </Button>
        <Link href="/">
          <Button variant="outline">返回首頁</Button>
        </Link>
      </div>
      
      {/* Display error information in development */}
      {process.env.NODE_ENV !== 'production' && (
        <div className="mt-8 p-4 bg-black/50 rounded-lg max-w-lg w-full overflow-auto text-left">
          <p className="text-red-400 font-mono text-sm break-all">
            {error.message || '未知錯誤'}
          </p>
          {error.digest && (
            <p className="text-gray-500 font-mono text-xs mt-2">
              Error ID: {error.digest}
            </p>
          )}
        </div>
      )}
    </div>
  );
}
