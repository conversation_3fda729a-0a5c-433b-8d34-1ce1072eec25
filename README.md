# 互動式學習課程報名網站

一個使用 Next.js 15+ 打造的現代化課程報名單頁式網站，整合了 3D 互動元素、表單驗證、資料庫儲存與自動電子郵件通知功能。

## 🚀 功能特點

- 現代化 UI 設計，深色系主題
- 互動式 3D 背景動畫 (React Three Fiber)
- 完整的表單驗證與錯誤處理
- 整合 PostgreSQL 資料庫儲存報名資料
- 自動發送確認電子郵件 (Resend API)
- 響應式設計，適合所有裝置
- 互動式動畫效果 (Framer Motion)
- 支援 SEO 優化

## 🧰 技術架構

- **前端框架**: Next.js 15+, React 18+, TypeScript
- **UI 庫**: shadcn/ui 0.9.4+, Tailwind CSS
- **3D 渲染**: React Three Fiber, Three.js
- **動畫**: Framer Motion
- **資料庫**: PostgreSQL 17+, Prisma ORM 6.2+
- **表單處理**: React Hook Form, Zod
- **電子郵件服務**: Resend

## 📋 專案結構

```
/
├── app/                      # App Router 路由與頁面
│   ├── api/                  # API 路由處理
│   │   └── register/         # 報名表單 API
│   ├── privacy/              # 隱私政策頁面
│   ├── page.tsx              # 主頁面
│   └── layout.tsx            # 根佈局
├── components/               # React 元件
│   ├── ui/                   # UI 基礎元件
│   ├── Hero.tsx              # 首頁頭部區塊
│   ├── HeroBackground.tsx    # 3D 背景動畫
│   ├── CourseInfo.tsx        # 課程資訊區塊
│   ├── RegistrationForm.tsx  # 報名表單
│   └── Footer.tsx            # 頁尾區塊
├── lib/                      # 功能模組
│   ├── prisma.ts             # Prisma 用戶端
│   ├── email.ts              # 電子郵件發送功能
│   └── utils.ts              # 通用工具函數
├── styles/                   # 樣式文件
│   └── globals.css           # 全域樣式
├── public/                   # 靜態資源
├── prisma/                   # Prisma 配置
│   └── schema.prisma         # 資料庫結構定義
├── next.config.js            # Next.js 配置
├── tailwind.config.ts        # Tailwind CSS 配置
├── tsconfig.json             # TypeScript 配置
└── package.json              # 專案依賴
```

## 🛠️ 安裝與設定

1. **安裝依賴**

   ```bash
   yarn install
   ```

2. **環境變數設定**

   在專案根目錄創建 `.env` 檔案:

   ```
   # Database
   DATABASE_URL="postgresql://user:password@localhost:5432/course_registration"
   
   # Resend API Key (用於電子郵件功能)
   RESEND_API_KEY="re_123456789"
   
   # 電子郵件域名
   EMAIL_DOMAIN="your-domain.com"
   ```

3. **初始化資料庫**

   ```bash
   npx prisma migrate dev --name init
   ```

4. **啟動開發伺服器**

   ```bash
   yarn dev
   ```

   應用程式將在 [http://localhost:3000](http://localhost:3000) 運行。

## 🚀 部署

### Vercel 部署

1. Fork 此專案到您的 GitHub 帳號
2. 在 [Vercel](https://vercel.com) 中導入該專案
3. 設定環境變數
4. 部署

### 資料庫設定

建議使用 [Neon](https://neon.tech) 作為 PostgreSQL 雲端解決方案:

1. 在 Neon 建立新專案
2. 使用提供的連接字符串作為 `DATABASE_URL` 環境變數
3. 執行遷移命令 `npx prisma migrate deploy`

## 📝 自定義

### 修改課程資訊

編輯 `components/CourseInfo.tsx` 檔案來更新課程詳情。

### 修改電子郵件範本

編輯 `lib/email.ts` 檔案中的範本來自定義確認郵件。

## 📄 授權

本專案基於 MIT 授權條款。

---

Powered by Next.js, React Three Fiber, and shadcn/ui.
