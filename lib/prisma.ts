/**
 * Prisma client singleton instance
 * Ensures a single connection to the database throughout the application
 */
import { PrismaClient } from "@prisma/client";

// Check if we're in production to avoid multiple connections in development
const globalForPrisma = globalThis as unknown as { prisma: PrismaClient };

// Create or reuse Prisma Client
export const prisma = globalForPrisma.prisma || new PrismaClient();

// Only save in global scope in development to prevent multiple connections
if (process.env.NODE_ENV !== "production") globalForPrisma.prisma = prisma;
