## 報名單頁式網站（Landing Page）功能與開發規格書（進階版）

### 🎯 專案目標

建立一個具有現代視覺風格、穩定功能與良好互動體驗的報名單頁式網站，採用 Next.js App Router 架構開發，整合資料儲存、自動 Email 通知，支援 SEO 與第三方追蹤工具。

---

### 🖼️ 首頁風格參考

* **參考網站**：[Vercel Blog - Interactive 3D Event Badge](https://vercel.com/blog/building-an-interactive-3d-event-badge-with-react-three-fiber)
* **視覺設計重點**：

  * 深色系主題，搭配線性漸層與光感動畫
  * Hero 段落使用大標題與高對比字體
  * 採用 Framer Motion 提供元件滑入/淡入動畫
  * 簡化版互動 3D 效果（使用 react-three-fiber 或動畫圖示）
  * 採用 Tailwind CSS 架構具一致性的設計系統
  * 適應式設計，手機版視圖採直式疊層

---

### 🧱 技術架構

* **前端框架**：Next.js 14 (App Router)
* **語言與工具**：TypeScript, Tailwind CSS
* **動畫**：Framer Motion, React Spring (選配)
* **UI 元件庫**：ShadCN/UI 或自定義元件
* **資料庫**：PostgreSQL（部署建議使用 Neon）
* **API 儲存**：使用 Next.js API Route，結合 Prisma ORM
* **信件服務**：Resend（支援 Vercel 無縫整合）
* **追蹤工具**：GA4、Meta Pixel、Vercel Analytics

---

### 📄 頁面與元件區塊

#### 1. Hero 區段

* 顯示活動 LOGO / 名稱（大標）
* 活動副標（15\~20 字摘要）
* CTA 按鈕（"立即報名"，滑動至表單區）
* 背景動畫：可為 3D 背景、粒子動畫或模糊光斑
* 自動偵測深/淺色主題，提供切換按鈕（選配）

#### 2. 活動介紹區段

* 三欄格：時間、地點、對象（使用 icon 呈現）
* 一段 80 字內說明內容的段落文字（支援 Markdown）
* 可插入主視覺圖片或影片（使用 <Image> 或 video tag）

#### 3. 報名表單區段（主區塊）

* 表單欄位：

  * 姓名（Text, 必填）
  * Email（Email, 必填，格式驗證）
  * 電話（Tel, 選填）
  * 備註留言（Textarea, 選填）
* 提交按鈕：顯示 loading spinner
* 表單驗證：

  * 前端使用 Zod 搭配 react-hook-form 驗證
  * 後端 Prisma Schema 驗證與資料寫入
* 成功後：顯示感謝訊息 + 自動滑至最上方

#### 4. 自動信件回覆邏輯

* 信件主旨：感謝您報名【活動名稱】
* 信件內容包含：

  * 報名者姓名 + 感謝語
  * 活動簡介、時間地點
  * 聯絡資訊、取消方式（若適用）
* 寄件人：noreply@活動網域（透過 Resend 設定）
* 錯誤處理：如信件失敗，顯示錯誤訊息 + log 儲存於資料庫

#### 5. Footer 區段

* 頁面導覽連結（活動說明、隱私權政策）
* 社群 icon（Facebook, Instagram, Email）
* 小字備註：「© 2025 活動主辦單位」

---

### 🔐 安全與防護機制

* Email 驗證避免重複提交（查詢 Email 是否存在）
* reCAPTCHA v3 或 Honeypot 防機器人填寫
* POST 請求加入 CSRF Token（NextAuth 可整合）
* 所有 Email 寫入與寄送都記錄 log 與回應碼

---

### 🧪 測試規劃

* 表單欄位異常輸入測試（空值、錯誤格式）
* 成功與失敗的 Email 傳送情境
* Database 是否正確儲存 + 寫入時間戳記
* 手機、平板、桌面版畫面顯示與互動測試
* 部署環境下測試 Vercel Function 運作與信件發送狀況

---

### 📦 部署與服務建議

* **平台部署**：Vercel
* **資料庫**：Neon（PostgreSQL 雲端免費方案）
* **Email 發送**：Resend
* **追蹤分析**：GA4 + Meta Pixel + Vercel Analytics

---

### 🔄 延伸功能建議

* 報名清單後台（需登入驗證）
* 活動分場次、座位編號
* 報名成功自動產生 QR Code
* 時間限制報名（報名期限截止自動隱藏表單）
* 支援 Stripe / TapPay 報名收費

---

### 📂 資料表 Schema 建議（PostgreSQL）

```sql
CREATE TABLE registrations (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  name VARCHAR(100) NOT NULL,
  email VARCHAR(150) NOT NULL,
  phone VARCHAR(30),
  message TEXT,
  submitted_at TIMESTAMPTZ DEFAULT CURRENT_TIMESTAMP,
  email_status VARCHAR(20) DEFAULT 'pending'
);
```

---


