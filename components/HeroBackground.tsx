/**
 * Interactive 3D event badge with physics-based lanyard
 * Based on Vercel's interactive 3D badge example
 * @see https://vercel.com/blog/building-an-interactive-3d-event-badge-with-react-three-fiber
 */
"use client";

import { useRef, useState, useEffect } from "react";
import { Can<PERSON>, useFrame, useThree, extend } from "@react-three/fiber";
import { useTexture, Environment, Lightformer } from "@react-three/drei";
import {
  BallCollider,
  CuboidCollider,
  Physics,
  RigidBody,
  useRopeJoint,
  useSphericalJoint
} from "@react-three/rapier";
import { MeshLineGeometry, MeshLineMaterial } from "meshline";
import * as THREE from "three";

// Extend R3F with MeshLine components
extend({ MeshLineGeometry, MeshLineMaterial });

// Preload assets
useTexture.preload('/models/band.jpg');

// MeshLine type declarations to fix TypeScript errors
declare global {
  namespace JSX {
    interface IntrinsicElements {
      meshLineGeometry: any;
      meshLineMaterial: any;
    }
  }
}

/**
 * Main interactive badge component with physics simulation
 * This component creates a badge with a lanyard that can be dragged around
 */
function Band({ maxSpeed = 50, minSpeed = 10 }: { maxSpeed?: number; minSpeed?: number }) {
  const band = useRef<THREE.Mesh>(null);
  const fixed = useRef<any>(null);
  const j1 = useRef<any>(null);
  const j2 = useRef<any>(null);
  const j3 = useRef<any>(null);
  const card = useRef<any>(null);

  const vec = new THREE.Vector3();
  const ang = new THREE.Vector3();
  const rot = new THREE.Vector3();
  const dir = new THREE.Vector3();

  const segmentProps = {
    type: 'dynamic' as const,
    canSleep: true,
    angularDamping: 2,
    linearDamping: 2
  };

  // Load textures
  const bandTexture = useTexture('/models/band.jpg');

  // Option 1: Use external badge image (uncomment if you have a badge image file)
  // const badgeTexture = useTexture('/textures/badge.png');

  // Option 2: Create badge texture programmatically
  const badgeTexture = new THREE.CanvasTexture((() => {
    const canvas = document.createElement('canvas');
    canvas.width = 512;
    canvas.height = 768; // Taller for badge proportions
    const ctx = canvas.getContext('2d')!;

    // Dark background
    ctx.fillStyle = '#2a2a2a';
    ctx.fillRect(0, 0, 512, 768);

    // White content
    ctx.fillStyle = '#ffffff';
    ctx.font = 'bold 24px Arial';
    ctx.textAlign = 'center';

    // Top triangle (simplified as text)
    ctx.font = 'bold 32px Arial';
    ctx.fillText('▲', 256, 120);

    // Large "S"
    ctx.font = 'bold 120px Arial';
    ctx.fillText('S', 256, 280);

    // Dots pattern
    for (let i = 0; i < 8; i++) {
      ctx.beginPath();
      ctx.arc(180 + i * 20, 320, 3, 0, Math.PI * 2);
      ctx.fill();
    }

    // "41"
    ctx.font = 'bold 60px Arial';
    ctx.textAlign = 'left';
    ctx.fillText('41', 180, 420);

    // Name
    ctx.font = 'bold 20px Arial';
    ctx.textAlign = 'center';
    ctx.fillText('Guillermo', 256, 520);
    ctx.fillText('Rauch', 256, 550);

    // Title
    ctx.font = '16px Arial';
    ctx.fillText('CEO & VERCEL', 256, 590);

    // Vercel logo (simplified)
    ctx.font = 'bold 24px Arial';
    ctx.fillText('▲ Vercel', 256, 640);

    // Small square
    ctx.fillRect(420, 700, 20, 20);

    return canvas;
  })());

  const { width, height } = useThree((state) => state.size);
  const [curve] = useState(() => new THREE.CatmullRomCurve3([
    new THREE.Vector3(),
    new THREE.Vector3(),
    new THREE.Vector3(),
    new THREE.Vector3(),
    new THREE.Vector3()
  ]));

  // Track if the badge is being dragged
  const [dragged, drag] = useState<THREE.Vector3 | false>(false);
  const [hovered, hover] = useState(false);

  // Set up physics joints to create the lanyard chain
  useRopeJoint(fixed, j1, [[0, -0.1, 0], [0, 0, 0], 1]);
  useRopeJoint(j1, j2, [[0, 0, 0], [0, 0, 0], 1]);
  useRopeJoint(j2, j3, [[0, 0, 0], [0, 0, 0], 1]);
  // Connect to the ring bottom on the badge using the same calculation logic
  // === ring 結構資訊 ===
  const ringLocal = new THREE.Vector3(0, 0.86, 0.025); // 金屬環在 badge 內的 local 座標
  const ringRadius = 0.06;
  const badgeOffset = new THREE.Vector3(0, -1.2, -0.05); // badge group 的位置偏移
  const scale = 2.25;

  // === 計算物理連接點 ===
  const ringWorldOffset = ringLocal.clone()
    .sub(new THREE.Vector3(0, ringRadius, 0)) // 接在環的底部
    .add(badgeOffset)
    .multiplyScalar(scale);

  useSphericalJoint(j3, card, [[0, 0, 0], ringWorldOffset.toArray()]);

  // Change cursor style when hovering
  useEffect(() => {
    if (hovered) {
      document.body.style.cursor = dragged ? 'grabbing' : 'grab';
      return () => { document.body.style.cursor = 'auto'; };
    }
  }, [hovered, dragged]);

  // Animation and physics simulation
  useFrame((state, delta) => {
    if (dragged) {
      // Calculate 3D position from pointer
      vec.set(state.pointer.x, state.pointer.y, 0.5).unproject(state.camera);
      dir.copy(vec).sub(state.camera.position).normalize();
      vec.add(dir.multiplyScalar(state.camera.position.length()));

      // Wake up all physics bodies to ensure they respond
      [card, j1, j2, j3, fixed].forEach((ref) => ref.current?.wakeUp());

      // Move the card to follow the pointer
      if (dragged instanceof THREE.Vector3) {
        card.current?.setNextKinematicTranslation({
          x: vec.x - dragged.x,
          y: vec.y - dragged.y,
          z: vec.z - dragged.z
        });
      }
    }

    if (fixed.current && band.current && card.current && j1.current && j2.current && j3.current) {
      // Fix most of the jitter when over pulling the card
      [j1, j2].forEach((ref) => {
        if (ref.current && !ref.current.lerped) ref.current.lerped = new THREE.Vector3().copy(ref.current.translation());
        if (ref.current && ref.current.lerped) {
          const clampedDistance = Math.max(0.1, Math.min(1, ref.current.lerped.distanceTo(ref.current.translation())));
          ref.current.lerped.lerp(ref.current.translation(), delta * (minSpeed + clampedDistance * (maxSpeed - minSpeed)));
        }
      });

      // Calculate catmul curve for natural connection
      // Start point: Connect to the bottom of the anchor ring
      const anchorRingPosition = new THREE.Vector3().copy(fixed.current.translation());
      anchorRingPosition.y -= 0.1; // Bottom of the ring
      curve.points[0].copy(anchorRingPosition);

      // Middle joints with smoothing
      curve.points[1].copy(j1.current.lerped);
      curve.points[2].copy(j2.current.lerped);
      curve.points[3].copy(j3.current.translation());

      // End point: Connect to the badge's metal ring bottom (considering scale and position)

      // === ring 結構資訊 ===
      const ringLocal = new THREE.Vector3(0, 0.86, 0.025); // 金屬環在 badge 內的 local 座標
      const ringRadius = 0.06;
      const badgeOffset = new THREE.Vector3(0, -1.2, -0.05); // badge group 的位置偏移
      const scale = 2.25;

      // === 轉換到 world 空間的正確掛點 ===
      const ringWorldOffset = ringLocal.clone()
        .sub(new THREE.Vector3(0, ringRadius, 0)) // 接在環的底部
        .add(badgeOffset)
        .multiplyScalar(scale);

      // === 將掛點加入 badge 當前世界位置 ===
      const badgeWorldPos = new THREE.Vector3().copy(card.current.translation());
      const finalAttachPoint = badgeWorldPos.add(ringWorldOffset);
      curve.points[4].copy(finalAttachPoint);

      // Update the mesh line geometry with the curve points (more points for smoother rope)
      (band.current as any).geometry.setPoints(curve.getPoints(64));

      // Apply forces to stabilize the card's rotation
      ang.copy(card.current.angvel());
      rot.copy(card.current.rotation());
      card.current.setAngvel({
        x: ang.x,
        y: ang.y - rot.y * 0.25,
        z: ang.z
      });
    }
  });

  // Set curve type and texture wrapping
  curve.curveType = 'chordal';
  bandTexture.wrapS = bandTexture.wrapT = THREE.RepeatWrapping;

  return (
    <>
      {/* Lanyard line connecting the joints - render first so it appears behind the badge */}
      <mesh ref={band}>
        <meshLineGeometry />
        <meshLineMaterial
          color="white"
          depthTest={true}
          resolution={[width, height]}
          useMap
          map={bandTexture}
          repeat={[-3, 1]}
          lineWidth={2}
        />
      </mesh>

      <group position={[-1, 4, 0]}>
        <RigidBody ref={fixed} {...segmentProps} type="fixed" />

        {/* Visible anchor ring */}
        <mesh position={[0, 0, 0]}>
          <torusGeometry args={[0.1, 0.025, 16, 32]} />
          <meshPhysicalMaterial
            color="#1a1a1a"
            roughness={0.2}
            metalness={0.9}
            emissive="#333"
            emissiveIntensity={0.1}
          />
        </mesh>
        <RigidBody position={[0.5, 0, 0]} ref={j1} {...segmentProps}>
          <BallCollider args={[0.1]} />
        </RigidBody>
        <RigidBody position={[1, 0, 0]} ref={j2} {...segmentProps}>
          <BallCollider args={[0.1]} />
        </RigidBody>
        <RigidBody position={[1.5, 0, 0]} ref={j3} {...segmentProps}>
          <BallCollider args={[0.1]} />
        </RigidBody>
        <RigidBody position={[2, 0, 0]} ref={card} {...segmentProps} type={dragged ? 'kinematicPosition' : 'dynamic'}>
          <CuboidCollider args={[0.8, 1.125, 0.01]} />
          <group
            scale={2.25}
            position={[0, -1.2, -0.05]}
            onPointerOver={() => hover(true)}
            onPointerOut={() => hover(false)}
            onPointerUp={(e) => ((e.target as any).releasePointerCapture(e.pointerId), drag(false))}
            onPointerDown={(e) => ((e.target as any).setPointerCapture(e.pointerId), drag(new THREE.Vector3().copy(e.point).sub(vec.copy(card.current.translation()))))}
          >
            <mesh>
              <boxGeometry args={[1, 1.6, 0.05]} />
              <meshPhysicalMaterial
                map={badgeTexture}
                clearcoat={1}
                clearcoatRoughness={0.15}
                roughness={0.3}
                metalness={0.5}
              />
            </mesh>
            <mesh position={[0, 0.8, 0.025]}>
              <boxGeometry args={[0.15, 0.08, 0.03]} />
              <meshPhysicalMaterial
                color="#1a1a1a"
                roughness={0.3}
                metalness={0.9}
              />
            </mesh>
            <mesh position={[0, 0.86, 0.025]}>
              <torusGeometry args={[0.06, 0.015, 8, 16]} />
              <meshPhysicalMaterial
                color="#1a1a1a"
                roughness={0.3}
                metalness={0.9}
              />
            </mesh>
            {/* Small connection point indicator at the bottom of the ring */}
            <mesh position={[0, 0.86 - 0.06, 0.025]}>
              <sphereGeometry args={[0.01]} />
              <meshPhysicalMaterial
                color="#ff0000"
                emissive="#ff0000"
                emissiveIntensity={0.5}
              />
            </mesh>
            {/* Green verification point for physics connection alignment */}
            <mesh position={[
              (0 + 0 - 1.2) * 2.25,  // ringLocal.x + badgeOffset.x scaled
              (0.86 - 0.06 - 1.2) * 2.25,  // (ringLocal.y - ringRadius + badgeOffset.y) scaled
              (0.025 - 0.05) * 2.25  // (ringLocal.z + badgeOffset.z) scaled
            ]}>
              <sphereGeometry args={[0.01]} />
              <meshStandardMaterial color="lime" emissive="lime" />
            </mesh>
          </group>
        </RigidBody>
      </group>
    </>
  );
}



/**
 * Main component export
 */
export default function HeroBackground() {
  return (
    <div className="absolute inset-0 select-none">
      <Canvas camera={{ position: [0, 0, 13], fov: 25 }}>
        <ambientLight intensity={Math.PI} />
        <Physics interpolate gravity={[0, -40, 0]} timeStep={1 / 60}>
          <Band />
        </Physics>
        <Environment background blur={0.75}>
          <color attach="background" args={['black']} />
          <Lightformer intensity={2} color="white" position={[0, -1, 5]} rotation={[0, 0, Math.PI / 3]} scale={[100, 0.1, 1]} />
          <Lightformer intensity={3} color="white" position={[-1, -1, 1]} rotation={[0, 0, Math.PI / 3]} scale={[100, 0.1, 1]} />
          <Lightformer intensity={3} color="white" position={[1, 1, 1]} rotation={[0, 0, Math.PI / 3]} scale={[100, 0.1, 1]} />
          <Lightformer intensity={10} color="white" position={[-10, 0, 14]} rotation={[0, Math.PI / 2, Math.PI / 3]} scale={[100, 10, 1]} />
        </Environment>
      </Canvas>
    </div>
  );
}