/**
 * Interactive 3D event badge with physics-based lanyard
 * Based on Vercel's interactive 3D badge example
 * @see https://vercel.com/blog/building-an-interactive-3d-event-badge-with-react-three-fiber
 */
"use client";

import { useRef, useState, useEffect } from "react";
import { Can<PERSON>, useFrame, useThree, extend } from "@react-three/fiber";
import { useTexture, Environment, Lightformer } from "@react-three/drei";
import {
  BallCollider,
  CuboidCollider,
  Physics,
  RigidBody,
  useRopeJoint,
  useSphericalJoint
} from "@react-three/rapier";
import { MeshLineGeometry, MeshLineMaterial } from "meshline";
import * as THREE from "three";

// Extend R3F with MeshLine components
extend({ MeshLineGeometry, MeshLineMaterial });

// Preload assets
useTexture.preload('/models/band.jpg');

// MeshLine type declarations to fix TypeScript errors
declare global {
  namespace JSX {
    interface IntrinsicElements {
      meshLineGeometry: any;
      meshLineMaterial: any;
    }
  }
}

/**
 * Main interactive badge component with physics simulation
 * This component creates a badge with a lanyard that can be dragged around
 */
function Band({ maxSpeed = 50, minSpeed = 10 }: { maxSpeed?: number; minSpeed?: number }) {
  // ⚙️ PHYSICS PARAMETERS CONFIGURATION
  const PHYSICS_CONFIG = {
    gravity: [0, -40, 0] as [number, number, number],       // Gravity force [x, y, z]
    timeStep: 1 / 60,           // Physics simulation precision
    angularDamping: 2,          // Rotation damping (higher = less spinning)
    linearDamping: 2,           // Movement damping (higher = more friction)
    jointLength: 1,             // Length of rope joints
    cardPosition: [2, 0, 0] as [number, number, number],    // Initial card position [x, y, z]
    chainPosition: [0, 4, 0] as [number, number, number],   // Chain anchor position [x, y, z]
  };

  // 🎨 MATERIAL EFFECTS CONFIGURATION
  const MATERIAL_CONFIG = {
    // Badge material settings
    badge: {
      clearcoat: 0.8,           // Glass-like coating (0-1)
      clearcoatRoughness: 0.2,  // Coating roughness (0-1)
      roughness: 0.1,           // Surface roughness (0-1)
      metalness: 0.1,           // Metallic appearance (0-1)
      emissive: '#000000',      // Glow color
      emissiveIntensity: 0,     // Glow strength (0-1)
    },
    // Metal clip material settings
    metal: {
      color: '#1a1a1a',         // Metal color
      roughness: 0.2,           // Metal roughness (0-1)
      metalness: 0.9,           // Metal strength (0-1)
      emissive: '#000000',      // Metal glow
      emissiveIntensity: 0,     // Metal glow strength
    }
  };

  // References for the badge physics elements
  const band = useRef<THREE.Mesh>(null);
  const fixed = useRef<any>(null);
  const j1 = useRef<any>(null);
  const j2 = useRef<any>(null);
  const j3 = useRef<any>(null);
  const card = useRef<any>(null);

  // Vectors for physics calculations
  const vec = new THREE.Vector3();
  const ang = new THREE.Vector3();
  const rot = new THREE.Vector3();
  const dir = new THREE.Vector3();

  // Segment properties for physics bodies (using config)
  const segmentProps = {
    type: 'dynamic' as const,
    canSleep: true,
    angularDamping: PHYSICS_CONFIG.angularDamping,
    linearDamping: PHYSICS_CONFIG.linearDamping
  };

  // Load textures
  const bandTexture = useTexture('/models/band.jpg');

  // 🖼️ BADGE TEXTURE CONFIGURATION
  // Set USE_EXTERNAL_IMAGE to true to use your own badge image
  const USE_EXTERNAL_IMAGE = false;

  // Option 1: Use external badge image (place your image in public/textures/badge.png)
  const externalBadgeTexture = USE_EXTERNAL_IMAGE ? useTexture('/textures/badge.png') : null;

  // Option 2: Create badge texture programmatically
  const programmaticBadgeTexture = new THREE.CanvasTexture((() => {
    const canvas = document.createElement('canvas');
    canvas.width = 512;
    canvas.height = 768; // Taller for badge proportions
    const ctx = canvas.getContext('2d')!;

    // Dark background
    ctx.fillStyle = '#2a2a2a';
    ctx.fillRect(0, 0, 512, 768);

    // White content
    ctx.fillStyle = '#ffffff';
    ctx.font = 'bold 24px Arial';
    ctx.textAlign = 'center';

    // Top triangle (simplified as text)
    ctx.font = 'bold 32px Arial';
    ctx.fillText('▲', 256, 120);

    // Large "S"
    ctx.font = 'bold 120px Arial';
    ctx.fillText('S', 256, 280);

    // Dots pattern
    for (let i = 0; i < 8; i++) {
      ctx.beginPath();
      ctx.arc(180 + i * 20, 320, 3, 0, Math.PI * 2);
      ctx.fill();
    }

    // "41"
    ctx.font = 'bold 60px Arial';
    ctx.textAlign = 'left';
    ctx.fillText('Join', 180, 420);

    // Name
    ctx.font = 'bold 20px Arial';
    ctx.textAlign = 'center';
    ctx.fillText('參加最棒的Hugo課程吧!', 256, 520);
    ctx.fillText('Enzo', 256, 550);

    // Title
    ctx.font = '16px Arial';
    ctx.fillText('CEO & VERCEL', 256, 590);

    // Vercel logo (simplified)
    ctx.font = 'bold 24px Arial';
    ctx.fillText('▲ Vercel', 256, 640);

    // Small square
    ctx.fillRect(420, 700, 20, 20);

    return canvas;
  })());

  // Choose which texture to use
  const badgeTexture = USE_EXTERNAL_IMAGE ? externalBadgeTexture : programmaticBadgeTexture;



  const { width, height } = useThree((state) => state.size);
  const [curve] = useState(() => new THREE.CatmullRomCurve3([
    new THREE.Vector3(),
    new THREE.Vector3(),
    new THREE.Vector3(),
    new THREE.Vector3()
  ]));

  // Track if the badge is being dragged
  const [dragged, drag] = useState<THREE.Vector3 | false>(false);
  const [hovered, hover] = useState(false);

  // 🔧 NEW FEATURES
  const [glowEffect, setGlowEffect] = useState(false);
  const [autoRotate, setAutoRotate] = useState(false);

  // Auto-rotation effect
  useEffect(() => {
    if (autoRotate && card.current && !dragged) {
      const interval = setInterval(() => {
        card.current?.applyTorqueImpulse({ x: 0, y: 0.1, z: 0 }, true);
      }, 100);
      return () => clearInterval(interval);
    }
  }, [autoRotate, dragged]);

  // Set up physics joints to create the lanyard chain (using config)
  // Connect from the ring bottom to the first joint
  useRopeJoint(fixed, j1, [[0, -0.075, 0], [0, 0, 0], PHYSICS_CONFIG.jointLength]);
  useRopeJoint(j1, j2, [[0, 0, 0], [0, 0, 0], PHYSICS_CONFIG.jointLength]);
  useRopeJoint(j2, j3, [[0, 0, 0], [0, 0, 0], PHYSICS_CONFIG.jointLength]);
  useSphericalJoint(j3, card, [[0, 0, 0], [0, 1.45, 0]]);

  // Change cursor style when hovering
  useEffect(() => {
    if (hovered) {
      document.body.style.cursor = dragged ? 'grabbing' : 'grab';
      return () => { document.body.style.cursor = 'auto'; };
    }
  }, [hovered, dragged]);

  // Animation and physics simulation
  useFrame((state, delta) => {
    if (dragged) {
      // Calculate 3D position from pointer
      vec.set(state.pointer.x, state.pointer.y, 0.5).unproject(state.camera);
      dir.copy(vec).sub(state.camera.position).normalize();
      vec.add(dir.multiplyScalar(state.camera.position.length()));

      // Wake up all physics bodies to ensure they respond
      [card, j1, j2, j3, fixed].forEach((ref) => ref.current?.wakeUp());

      // Move the card to follow the pointer
      if (dragged instanceof THREE.Vector3) {
        card.current?.setNextKinematicTranslation({
          x: vec.x - dragged.x,
          y: vec.y - dragged.y,
          z: vec.z - dragged.z
        });
      }
    }

    if (fixed.current && band.current) {
      // Fix most of the jitter when over pulling the card
      [j1, j2].forEach((ref) => {
        if (!ref.current.lerped) ref.current.lerped = new THREE.Vector3().copy(ref.current.translation());
        const clampedDistance = Math.max(0.1, Math.min(1, ref.current.lerped.distanceTo(ref.current.translation())));
        ref.current.lerped.lerp(ref.current.translation(), delta * (minSpeed + clampedDistance * (maxSpeed - minSpeed)));
      });

      // Calculate catmul curve - connect to the ring bottom
      curve.points[0].copy(j3.current.translation());
      curve.points[1].copy(j2.current.lerped);
      curve.points[2].copy(j1.current.lerped);
      // Connect to the bottom of the ring
      const ringBottomPosition = new THREE.Vector3().copy(fixed.current.translation());
      ringBottomPosition.y -= 0.075; // Ring radius + small offset
      curve.points[3].copy(ringBottomPosition);

      // Update the mesh line geometry with the curve points
      (band.current as any).geometry.setPoints(curve.getPoints(32));

      // Apply forces to stabilize the card's rotation
      ang.copy(card.current.angvel());
      rot.copy(card.current.rotation());
      card.current.setAngvel({
        x: ang.x,
        y: ang.y - rot.y * 0.25,
        z: ang.z
      });
    }
  });

  // Set curve type and texture wrapping
  curve.curveType = 'chordal';
  bandTexture.wrapS = bandTexture.wrapT = THREE.RepeatWrapping;

  return (
    <>
      {/* Physics joint chain positioned at the top of the screen */}
      <group position={PHYSICS_CONFIG.chainPosition}>
        {/* Fixed anchor point - positioned exactly at the ring location */}
        <RigidBody ref={fixed} {...segmentProps} type="fixed" />

        {/* Metal ring at the anchor point - where lanyard connects */}
        <mesh position={[0, 0, 0]}>
          <torusGeometry args={[0.06, 0.015, 8, 16]} />
          <meshPhysicalMaterial
            color={MATERIAL_CONFIG.metal.color}
            roughness={MATERIAL_CONFIG.metal.roughness}
            metalness={MATERIAL_CONFIG.metal.metalness}
            emissive={MATERIAL_CONFIG.metal.emissive}
            emissiveIntensity={MATERIAL_CONFIG.metal.emissiveIntensity}
          />
        </mesh>

        {/* Chain of joints that form the physics-based lanyard */}
        <RigidBody position={[0.5, 0, 0]} ref={j1} {...segmentProps}>
          <BallCollider args={[0.1]} />
        </RigidBody>

        <RigidBody position={[1, 0, 0]} ref={j2} {...segmentProps}>
          <BallCollider args={[0.1]} />
        </RigidBody>

        <RigidBody position={[1.5, 0, 0]} ref={j3} {...segmentProps}>
          <BallCollider args={[0.1]} />
        </RigidBody>

        {/* The badge card that can be dragged */}
        <RigidBody
          position={PHYSICS_CONFIG.cardPosition}
          ref={card}
          {...segmentProps}
          type={dragged ? 'kinematicPosition' : 'dynamic'}
        >
          <CuboidCollider args={[0.8, 1.125, 0.01]} />

          {/* Visible badge with interaction handlers */}
          <group
            scale={2.25}
            position={[0, -1.2, -0.05]}
            onPointerOver={() => hover(true)}
            onPointerOut={() => hover(false)}
            onPointerUp={(e) => ((e.target as any).releasePointerCapture(e.pointerId), drag(false))}
            onPointerDown={(e) => ((e.target as any).setPointerCapture(e.pointerId), drag(new THREE.Vector3().copy(e.point).sub(vec.copy(card.current.translation()))))}
          >
            {/* Main badge card with texture */}
            <mesh>
              <boxGeometry args={[1, 1.6, 0.05]} />
              <meshPhysicalMaterial
                map={badgeTexture}
                clearcoat={MATERIAL_CONFIG.badge.clearcoat}
                clearcoatRoughness={MATERIAL_CONFIG.badge.clearcoatRoughness}
                roughness={MATERIAL_CONFIG.badge.roughness}
                metalness={MATERIAL_CONFIG.badge.metalness}
                emissive={MATERIAL_CONFIG.badge.emissive}
                emissiveIntensity={MATERIAL_CONFIG.badge.emissiveIntensity}
              />
            </mesh>

            {/* Metal clip with ring - positioned to connect with lanyard */}
            <group position={[0, 0.6, 0]}>
              {/* Clip body - attached to the badge */}
              <mesh position={[0, 0, 0.03]}>
                <boxGeometry args={[0.15, 0.08, 0.03]} />
                <meshPhysicalMaterial
                  color={MATERIAL_CONFIG.metal.color}
                  roughness={MATERIAL_CONFIG.metal.roughness}
                  metalness={MATERIAL_CONFIG.metal.metalness}
                  emissive={MATERIAL_CONFIG.metal.emissive}
                  emissiveIntensity={MATERIAL_CONFIG.metal.emissiveIntensity}
                />
              </mesh>

              {/* Connector extending upward */}
              <mesh position={[0, 0.1, 0.03]}>
                <cylinderGeometry args={[0.02, 0.02, 0.2]} />
                <meshPhysicalMaterial
                  color={MATERIAL_CONFIG.metal.color}
                  roughness={MATERIAL_CONFIG.metal.roughness}
                  metalness={MATERIAL_CONFIG.metal.metalness}
                  emissive={MATERIAL_CONFIG.metal.emissive}
                  emissiveIntensity={MATERIAL_CONFIG.metal.emissiveIntensity}
                />
              </mesh>
            </group>
          </group>
        </RigidBody>
      </group>

      {/* Lanyard line connecting the joints */}
      <mesh ref={band}>
        <meshLineGeometry />
        <meshLineMaterial
          color="white"
          depthTest={false}
          resolution={[width, height]}
          useMap
          map={bandTexture}
          repeat={[-3, 1]}
          lineWidth={1}
        />
      </mesh>
    </>
  );
}



/**
 * Main component export
 */
export default function HeroBackground() {
  return (
    <div className="absolute inset-0 select-none">
      <Canvas camera={{ position: [0, 0, 13], fov: 25 }}>
        <ambientLight intensity={Math.PI} />
        <Physics
          interpolate
          gravity={[0, -40, 0]}
          timeStep={1 / 60}
        >
          <Band />
        </Physics>
        <Environment background blur={0.75}>
          <color attach="background" args={['black']} />
          <Lightformer intensity={2} color="white" position={[0, -1, 5]} rotation={[0, 0, Math.PI / 3]} scale={[100, 0.1, 1]} />
          <Lightformer intensity={3} color="white" position={[-1, -1, 1]} rotation={[0, 0, Math.PI / 3]} scale={[100, 0.1, 1]} />
          <Lightformer intensity={3} color="white" position={[1, 1, 1]} rotation={[0, 0, Math.PI / 3]} scale={[100, 0.1, 1]} />
          <Lightformer intensity={10} color="white" position={[-10, 0, 14]} rotation={[0, Math.PI / 2, Math.PI / 3]} scale={[100, 10, 1]} />
        </Environment>
      </Canvas>
    </div>
  );
}