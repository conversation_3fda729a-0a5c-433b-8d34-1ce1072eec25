/**
 * Interactive 3D event badge with physics-based lanyard
 * Based on Vercel's interactive 3D badge example
 * @see https://vercel.com/blog/building-an-interactive-3d-event-badge-with-react-three-fiber
 */
"use client";

import { useRef, useState, useEffect } from "react";
import { Can<PERSON>, useFrame, useThree, extend } from "@react-three/fiber";
import { useTexture, Environment, Lightformer } from "@react-three/drei";
import {
  BallCollider,
  CuboidCollider,
  Physics,
  RigidBody,
  useRopeJoint,
  useSphericalJoint
} from "@react-three/rapier";
import { MeshLineGeometry, MeshLineMaterial } from "meshline";
import * as THREE from "three";

// Extend R3F with MeshLine components
extend({ MeshLineGeometry, MeshLineMaterial });

// Preload assets
useTexture.preload('/models/band.jpg');

// MeshLine type declarations to fix TypeScript errors
declare global {
  namespace JSX {
    interface IntrinsicElements {
      meshLineGeometry: any;
      meshLineMaterial: any;
    }
  }
}

/**
 * Main interactive badge component with physics simulation
 * This component creates a badge with a lanyard that can be dragged around
 */
function Band({ maxSpeed = 50, minSpeed = 10 }: { maxSpeed?: number; minSpeed?: number }) {
  // References for the badge physics elements
  const band = useRef<THREE.Mesh>(null);
  const fixed = useRef<any>(null);
  const j1 = useRef<any>(null);
  const j2 = useRef<any>(null);
  const j3 = useRef<any>(null);
  const card = useRef<any>(null);

  // Vectors for physics calculations
  const vec = new THREE.Vector3();
  const ang = new THREE.Vector3();
  const rot = new THREE.Vector3();
  const dir = new THREE.Vector3();

  // Segment properties for physics bodies
  const segmentProps = {
    type: 'dynamic' as const,
    canSleep: true,
    angularDamping: 2,
    linearDamping: 2
  };

  // Load texture for the lanyard
  const texture = useTexture('/models/band.jpg');

  const { width, height } = useThree((state) => state.size);
  const [curve] = useState(() => new THREE.CatmullRomCurve3([
    new THREE.Vector3(),
    new THREE.Vector3(),
    new THREE.Vector3(),
    new THREE.Vector3()
  ]));

  // Track if the badge is being dragged
  const [dragged, drag] = useState<THREE.Vector3 | false>(false);
  const [hovered, hover] = useState(false);

  // Set up physics joints to create the lanyard chain
  useRopeJoint(fixed, j1, [[0, 0, 0], [0, 0, 0], 1]);
  useRopeJoint(j1, j2, [[0, 0, 0], [0, 0, 0], 1]);
  useRopeJoint(j2, j3, [[0, 0, 0], [0, 0, 0], 1]);
  useSphericalJoint(j3, card, [[0, 0, 0], [0, 1.45, 0]]);

  // Change cursor style when hovering
  useEffect(() => {
    if (hovered) {
      document.body.style.cursor = dragged ? 'grabbing' : 'grab';
      return () => { document.body.style.cursor = 'auto'; };
    }
  }, [hovered, dragged]);

  // Animation and physics simulation
  useFrame((state, delta) => {
    if (dragged) {
      // Calculate 3D position from pointer
      vec.set(state.pointer.x, state.pointer.y, 0.5).unproject(state.camera);
      dir.copy(vec).sub(state.camera.position).normalize();
      vec.add(dir.multiplyScalar(state.camera.position.length()));

      // Wake up all physics bodies to ensure they respond
      [card, j1, j2, j3, fixed].forEach((ref) => ref.current?.wakeUp());

      // Move the card to follow the pointer
      if (dragged instanceof THREE.Vector3) {
        card.current?.setNextKinematicTranslation({
          x: vec.x - dragged.x,
          y: vec.y - dragged.y,
          z: vec.z - dragged.z
        });
      }
    }

    if (fixed.current && band.current) {
      // Fix most of the jitter when over pulling the card
      [j1, j2].forEach((ref) => {
        if (!ref.current.lerped) ref.current.lerped = new THREE.Vector3().copy(ref.current.translation());
        const clampedDistance = Math.max(0.1, Math.min(1, ref.current.lerped.distanceTo(ref.current.translation())));
        ref.current.lerped.lerp(ref.current.translation(), delta * (minSpeed + clampedDistance * (maxSpeed - minSpeed)));
      });

      // Calculate catmul curve
      curve.points[0].copy(j3.current.translation());
      curve.points[1].copy(j2.current.lerped);
      curve.points[2].copy(j1.current.lerped);
      curve.points[3].copy(fixed.current.translation());

      // Update the mesh line geometry with the curve points
      (band.current as any).geometry.setPoints(curve.getPoints(32));

      // Apply forces to stabilize the card's rotation
      ang.copy(card.current.angvel());
      rot.copy(card.current.rotation());
      card.current.setAngvel({
        x: ang.x,
        y: ang.y - rot.y * 0.25,
        z: ang.z
      });
    }
  });

  // Set curve type and texture wrapping
  curve.curveType = 'chordal';
  texture.wrapS = texture.wrapT = THREE.RepeatWrapping;

  return (
    <>
      {/* Physics joint chain positioned at the top of the screen */}
      <group position={[0, 4, 0]}>
        {/* Fixed anchor point */}
        <RigidBody ref={fixed} {...segmentProps} type="fixed" />

        {/* Chain of joints that form the physics-based lanyard */}
        <RigidBody position={[0.5, 0, 0]} ref={j1} {...segmentProps}>
          <BallCollider args={[0.1]} />
        </RigidBody>

        <RigidBody position={[1, 0, 0]} ref={j2} {...segmentProps}>
          <BallCollider args={[0.1]} />
        </RigidBody>

        <RigidBody position={[1.5, 0, 0]} ref={j3} {...segmentProps}>
          <BallCollider args={[0.1]} />
        </RigidBody>

        {/* The badge card that can be dragged */}
        <RigidBody
          position={[2, 0, 0]}
          ref={card}
          {...segmentProps}
          type={dragged ? 'kinematicPosition' : 'dynamic'}
        >
          <CuboidCollider args={[0.8, 1.125, 0.01]} />

          {/* Visible badge with interaction handlers */}
          <group
            scale={2.25}
            position={[0, -1.2, -0.05]}
            onPointerOver={() => hover(true)}
            onPointerOut={() => hover(false)}
            onPointerUp={(e) => ((e.target as any).releasePointerCapture(e.pointerId), drag(false))}
            onPointerDown={(e) => ((e.target as any).setPointerCapture(e.pointerId), drag(new THREE.Vector3().copy(e.point).sub(vec.copy(card.current.translation()))))}
          >
            {/* Main badge card - dark background */}
            <mesh>
              <boxGeometry args={[1, 1.6, 0.05]} />
              <meshPhysicalMaterial
                color="#2a2a2a"
                clearcoat={0.8}
                clearcoatRoughness={0.2}
                roughness={0.1}
                metalness={0.1}
              />
            </mesh>

            {/* Top triangle logo */}
            <group position={[0, 0.6, 0.026]}>
              <mesh>
                <coneGeometry args={[0.08, 0.12, 3]} />
                <meshBasicMaterial color="#ffffff" />
              </mesh>
            </group>

            {/* Large "S" letter */}
            <group position={[0, 0.25, 0.026]}>
              <mesh>
                <planeGeometry args={[0.25, 0.35]} />
                <meshBasicMaterial color="#ffffff" transparent opacity={0.95} />
              </mesh>
            </group>

            {/* Dotted pattern under S */}
            <group position={[0, 0.05, 0.026]}>
              {Array.from({ length: 8 }, (_, i) => (
                <mesh key={i} position={[(i - 3.5) * 0.04, 0, 0]}>
                  <sphereGeometry args={[0.008]} />
                  <meshBasicMaterial color="#ffffff" />
                </mesh>
              ))}
            </group>

            {/* "41" text */}
            <group position={[-0.15, -0.15, 0.026]}>
              <mesh>
                <planeGeometry args={[0.12, 0.2]} />
                <meshBasicMaterial color="#ffffff" transparent opacity={0.95} />
              </mesh>
            </group>

            {/* Name area - "Guillermo Rauch" */}
            <group position={[0, -0.4, 0.026]}>
              <mesh>
                <planeGeometry args={[0.7, 0.08]} />
                <meshBasicMaterial color="#ffffff" transparent opacity={0.95} />
              </mesh>
            </group>

            {/* "CEO & VERCEL" text */}
            <group position={[0, -0.52, 0.026]}>
              <mesh>
                <planeGeometry args={[0.5, 0.05]} />
                <meshBasicMaterial color="#ffffff" transparent opacity={0.8} />
              </mesh>
            </group>

            {/* Bottom "Vercel" logo */}
            <group position={[0, -0.65, 0.026]}>
              <mesh>
                <planeGeometry args={[0.4, 0.06]} />
                <meshBasicMaterial color="#ffffff" transparent opacity={0.9} />
              </mesh>
            </group>

            {/* Small bottom right square */}
            <group position={[0.35, -0.7, 0.026]}>
              <mesh>
                <boxGeometry args={[0.04, 0.04, 0.01]} />
                <meshBasicMaterial color="#ffffff" />
              </mesh>
            </group>

            {/* Metal clip with ring */}
            <group position={[0, 0.8, 0]}>
              {/* Clip body */}
              <mesh position={[0, 0, 0.03]}>
                <boxGeometry args={[0.15, 0.08, 0.03]} />
                <meshPhysicalMaterial
                  color="#1a1a1a"
                  roughness={0.2}
                  metalness={0.9}
                />
              </mesh>

              {/* Ring */}
              <mesh position={[0, 0.06, 0.03]}>
                <torusGeometry args={[0.06, 0.015, 8, 16]} />
                <meshPhysicalMaterial
                  color="#1a1a1a"
                  roughness={0.2}
                  metalness={0.9}
                />
              </mesh>

              {/* Small connector */}
              <mesh position={[0, 0.02, 0.03]}>
                <cylinderGeometry args={[0.02, 0.02, 0.04]} />
                <meshPhysicalMaterial
                  color="#1a1a1a"
                  roughness={0.2}
                  metalness={0.9}
                />
              </mesh>
            </group>
          </group>
        </RigidBody>
      </group>

      {/* Lanyard line connecting the joints */}
      <mesh ref={band}>
        <meshLineGeometry />
        <meshLineMaterial
          color="white"
          depthTest={false}
          resolution={[width, height]}
          useMap
          map={texture}
          repeat={[-3, 1]}
          lineWidth={1}
        />
      </mesh>
    </>
  );
}



/**
 * Main component export
 */
export default function HeroBackground() {
  return (
    <div className="absolute inset-0 select-none">
      <Canvas camera={{ position: [0, 0, 13], fov: 25 }}>
        <ambientLight intensity={Math.PI} />
        <Physics interpolate gravity={[0, -40, 0]} timeStep={1 / 60}>
          <Band />
        </Physics>
        <Environment background blur={0.75}>
          <color attach="background" args={['black']} />
          <Lightformer intensity={2} color="white" position={[0, -1, 5]} rotation={[0, 0, Math.PI / 3]} scale={[100, 0.1, 1]} />
          <Lightformer intensity={3} color="white" position={[-1, -1, 1]} rotation={[0, 0, Math.PI / 3]} scale={[100, 0.1, 1]} />
          <Lightformer intensity={3} color="white" position={[1, 1, 1]} rotation={[0, 0, Math.PI / 3]} scale={[100, 0.1, 1]} />
          <Lightformer intensity={10} color="white" position={[-10, 0, 14]} rotation={[0, Math.PI / 2, Math.PI / 3]} scale={[100, 10, 1]} />
        </Environment>
      </Canvas>
    </div>
  );
}