/**
 * Analytics component for Google Analytics 4 and Meta Pixel integration
 * Adds tracking scripts to the application
 */
'use client';

import { useEffect } from 'react';
import Script from 'next/script';
import { usePathname } from 'next/navigation';

declare global {
  interface Window {
    gtag: any;
    fbq: any;
  }
}

/**
 * Track page views in Google Analytics and Meta Pixel
 * @param url - Current URL to track
 */
export const trackPageview = (url: string) => {
  // Google Analytics pageview tracking
  if (window.gtag) {
    window.gtag('config', process.env.NEXT_PUBLIC_GA_MEASUREMENT_ID as string, {
      page_path: url,
    });
  }

  // Meta Pixel pageview tracking
  if (window.fbq) {
    window.fbq('track', 'PageView');
  }
};

/**
 * Track custom events in Google Analytics and Meta Pixel
 * @param eventName - Name of the event to track
 * @param parameters - Additional parameters for the event
 */
export const trackEvent = (eventName: string, parameters?: Record<string, any>) => {
  // Google Analytics event tracking
  if (window.gtag) {
    window.gtag('event', eventName, parameters);
  }

  // Meta Pixel event tracking
  if (window.fbq) {
    window.fbq('track', eventName, parameters);
  }
};

interface AnalyticsProps {
  gaMeasurementId?: string;
  metaPixelId?: string;
}

export default function Analytics({ 
  gaMeasurementId = process.env.NEXT_PUBLIC_GA_MEASUREMENT_ID,
  metaPixelId = process.env.NEXT_PUBLIC_META_PIXEL_ID 
}: AnalyticsProps) {
  const pathname = usePathname();

  useEffect(() => {
    // Track page views whenever pathname changes
    if ((gaMeasurementId || metaPixelId) && pathname) {
      trackPageview(pathname);
    }
  }, [pathname, gaMeasurementId, metaPixelId]);

  if (!gaMeasurementId && !metaPixelId) return null;

  return (
    <>
      {/* Google Analytics Script */}
      {gaMeasurementId && (
        <>
          <Script
            src={`https://www.googletagmanager.com/gtag/js?id=${gaMeasurementId}`}
            strategy="afterInteractive"
          />
          <Script id="google-analytics" strategy="afterInteractive">
            {`
              window.dataLayer = window.dataLayer || [];
              function gtag(){dataLayer.push(arguments);}
              gtag('js', new Date());
              gtag('config', '${gaMeasurementId}', {
                page_path: window.location.pathname,
                cookie_flags: 'SameSite=None;Secure',
              });
            `}
          </Script>
        </>
      )}

      {/* Meta Pixel Script */}
      {metaPixelId && (
        <Script id="meta-pixel" strategy="afterInteractive">
          {`
            !function(f,b,e,v,n,t,s)
            {if(f.fbq)return;n=f.fbq=function(){n.callMethod?
            n.callMethod.apply(n,arguments):n.queue.push(arguments)};
            if(!f._fbq)f._fbq=n;n.push=n;n.loaded=!0;n.version='2.0';
            n.queue=[];t=b.createElement(e);t.async=!0;
            t.src=v;s=b.getElementsByTagName(e)[0];
            s.parentNode.insertBefore(t,s)}(window, document,'script',
            'https://connect.facebook.net/en_US/fbevents.js');
            fbq('init', '${metaPixelId}');
            fbq('track', 'PageView');
          `}
        </Script>
      )}
    </>
  );
}
