/**
 * Theme provider component that manages color theme settings
 * Uses next-themes for theme management with light/dark mode support
 */
"use client";

import * as React from "react";
import { ThemeProvider as NextThemesProvider } from "next-themes";
// import { type ThemeProviderProps } from "next-themes/dist/types"; // Removed: not needed, causes error

export function ThemeProvider({ children, ...props }: React.PropsWithChildren<React.ComponentProps<typeof NextThemesProvider>>) {
  return <NextThemesProvider {...props}>{children}</NextThemesProvider>;
}
