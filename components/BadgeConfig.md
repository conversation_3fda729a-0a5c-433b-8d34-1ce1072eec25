# 🎮 3D 徽章自定義指南

## 🖼️ 更換徽章圖片

### 方法一：使用外部圖片
1. 將您的徽章圖片放入 `public/textures/badge.png`
2. 在 `HeroBackground.tsx` 中設置：
```tsx
const USE_EXTERNAL_IMAGE = true;
```

### 方法二：自定義程序化紋理
修改 `programmaticBadgeTexture` 中的繪製代碼：
```tsx
// 更改背景顏色
ctx.fillStyle = '#your-color';

// 添加自定義文字
ctx.fillText('您的文字', x, y);

// 添加圖形
ctx.beginPath();
ctx.arc(x, y, radius, 0, Math.PI * 2);
ctx.fill();
```

## 🎨 調整材質效果

### 徽章材質設置
```tsx
const MATERIAL_CONFIG = {
  badge: {
    clearcoat: 0.8,           // 玻璃塗層 (0-1)
    clearcoatRoughness: 0.2,  // 塗層粗糙度 (0-1)
    roughness: 0.1,           // 表面粗糙度 (0-1)
    metalness: 0.1,           // 金屬感 (0-1)
    emissive: '#ff0000',      // 發光顏色
    emissiveIntensity: 0.5,   // 發光強度 (0-1)
  }
}
```

### 金屬夾材質設置
```tsx
metal: {
  color: '#silver',         // 金屬顏色
  roughness: 0.1,          // 金屬粗糙度 (0-1)
  metalness: 1.0,          // 金屬強度 (0-1)
  emissive: '#0000ff',     // 金屬發光
  emissiveIntensity: 0.2,  // 發光強度
}
```

## ⚙️ 修改物理參數

### 重力和運動
```tsx
const PHYSICS_CONFIG = {
  gravity: [0, -60, 0],        // 更強重力
  angularDamping: 5,           // 更多旋轉阻尼
  linearDamping: 3,            // 更多移動阻尼
  jointLength: 1.5,            // 更長的繩索
  cardPosition: [3, 0, 0],     // 徽章初始位置
  chainPosition: [0, 5, 0],    // 鏈條錨點位置
}
```

### 互動響應
- `angularDamping`: 數值越高，旋轉越快停止
- `linearDamping`: 數值越高，移動越快停止
- `gravity`: 調整重力方向和強度
- `jointLength`: 調整繩索段長度

## 🔧 添加新功能

### 1. 發光效果
```tsx
// 在材質中啟用發光
emissive: glowEffect ? '#ff6600' : '#000000',
emissiveIntensity: glowEffect ? 0.8 : 0,

// 切換發光
setGlowEffect(!glowEffect);
```

### 2. 自動旋轉
```tsx
// 啟用自動旋轉
setAutoRotate(true);

// 自定義旋轉速度
card.current?.applyTorqueImpulse({ x: 0, y: speed, z: 0 }, true);
```

### 3. 顏色變化
```tsx
// 動態顏色變化
const [currentColor, setCurrentColor] = useState('#4c21e0');

// 隨機顏色
const randomColor = () => {
  const colors = ['#ff0000', '#00ff00', '#0000ff', '#ffff00'];
  setCurrentColor(colors[Math.floor(Math.random() * colors.length)]);
};
```

### 4. 音效支援
```tsx
// 添加拖拽音效
const playSound = (soundFile: string) => {
  const audio = new Audio(`/sounds/${soundFile}`);
  audio.play();
};

// 在拖拽事件中使用
onPointerDown={() => playSound('grab.mp3')}
onPointerUp={() => playSound('release.mp3')}
```

### 5. 粒子效果
```tsx
// 添加粒子系統
import { Points, PointMaterial } from '@react-three/drei';

// 在徽章周圍添加粒子
<Points>
  <pointsGeometry args={[particles]} />
  <PointMaterial 
    size={0.05} 
    color="#ffffff" 
    transparent 
    opacity={0.6} 
  />
</Points>
```

## 🎯 實用技巧

### 性能優化
- 使用 `canSleep: true` 讓靜止物體休眠
- 調整 `timeStep` 平衡性能和精度
- 使用 `useMemo` 快取複雜計算

### 視覺效果
- 調整 `Environment` 的 `blur` 值改變背景模糊
- 修改 `Lightformer` 位置和強度調整照明
- 使用 `fog` 添加深度感

### 互動體驗
- 調整碰撞器大小改變拖拽感受
- 修改關節參數調整擺動效果
- 添加視覺回饋提升互動性

## 📱 響應式設計

### 移動端優化
```tsx
// 檢測設備類型
const isMobile = /iPhone|iPad|iPod|Android/i.test(navigator.userAgent);

// 調整物理參數
const mobilePhysics = {
  gravity: isMobile ? [0, -30, 0] : [0, -40, 0],
  sensitivity: isMobile ? 1.5 : 1.0,
};
```

### 螢幕尺寸適應
```tsx
// 根據螢幕大小調整徽章尺寸
const { width, height } = useThree((state) => state.size);
const scale = Math.min(width / 1920, height / 1080);

<group scale={[scale, scale, scale]}>
  {/* 徽章內容 */}
</group>
```
